"""
数据库管理模块
"""
import sqlite3
import pandas as pd
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Float, Integer, Date, DateTime, Index
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from typing import Optional, Dict, Any, List
from loguru import logger
from src.config import config

Base = declarative_base()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.config = config.database_config
        self.engine = None
        self.session_maker = None
        self._connect()
    
    def _connect(self):
        """建立数据库连接"""
        db_type = self.config.get('type', 'sqlite')
        
        if db_type == 'sqlite':
            db_path = self.config.get('sqlite', {}).get('path', 'data/factor_database.db')
            connection_string = f"sqlite:///{db_path}"
        elif db_type == 'mysql':
            mysql_config = self.config.get('mysql', {})
            connection_string = (
                f"mysql+pymysql://{mysql_config.get('username')}:"
                f"{mysql_config.get('password')}@{mysql_config.get('host')}:"
                f"{mysql_config.get('port')}/{mysql_config.get('database')}"
                f"?charset={mysql_config.get('charset', 'utf8mb4')}"
            )
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
        
        try:
            self.engine = create_engine(connection_string, echo=False)
            self.session_maker = sessionmaker(bind=self.engine)
            logger.info(f"数据库连接成功: {db_type}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def create_tables(self):
        """创建所有表"""
        try:
            # 创建基础信息表
            self._create_stock_basic_table()
            
            # 创建交易日历表
            self._create_trade_calendar_table()
            
            # 创建日线行情表
            self._create_daily_basic_table()
            
            # 创建财务数据表
            self._create_financial_tables()
            
            # 创建因子表
            self._create_factor_tables()
            
            logger.info("所有数据表创建完成")
            
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            raise
    
    def _create_stock_basic_table(self):
        """创建股票基本信息表"""
        sql = """
        CREATE TABLE IF NOT EXISTS stock_basic (
            ts_code VARCHAR(20) PRIMARY KEY,
            symbol VARCHAR(10),
            name VARCHAR(50),
            area VARCHAR(20),
            industry VARCHAR(50),
            market VARCHAR(10),
            list_date DATE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        self._execute_sql(sql)
        
        # 创建索引
        self._create_index('stock_basic', ['industry', 'area', 'market'])
    
    def _create_trade_calendar_table(self):
        """创建交易日历表"""
        sql = """
        CREATE TABLE IF NOT EXISTS trade_calendar (
            cal_date DATE PRIMARY KEY,
            is_open INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        self._execute_sql(sql)
    
    def _create_daily_basic_table(self):
        """创建日线基础数据表"""
        sql = """
        CREATE TABLE IF NOT EXISTS daily_basic (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ts_code VARCHAR(20),
            trade_date DATE,
            close FLOAT,
            turnover_rate FLOAT,
            turnover_rate_f FLOAT,
            volume_ratio FLOAT,
            pe FLOAT,
            pe_ttm FLOAT,
            pb FLOAT,
            ps FLOAT,
            ps_ttm FLOAT,
            dv_ratio FLOAT,
            dv_ttm FLOAT,
            total_share FLOAT,
            float_share FLOAT,
            free_share FLOAT,
            total_mv FLOAT,
            circ_mv FLOAT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ts_code, trade_date)
        )
        """
        self._execute_sql(sql)
        
        # 创建索引
        self._create_index('daily_basic', ['ts_code', 'trade_date'])
        self._create_index('daily_basic', ['trade_date'])
    
    def _create_financial_tables(self):
        """创建财务数据表"""
        # 利润表
        income_sql = """
        CREATE TABLE IF NOT EXISTS income_statement (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ts_code VARCHAR(20),
            ann_date DATE,
            f_ann_date DATE,
            end_date DATE,
            report_type VARCHAR(10),
            comp_type VARCHAR(10),
            total_revenue FLOAT,
            revenue FLOAT,
            int_income FLOAT,
            prem_earned FLOAT,
            comm_income FLOAT,
            n_commis_income FLOAT,
            n_oth_income FLOAT,
            n_oth_b_income FLOAT,
            prem_income FLOAT,
            out_prem FLOAT,
            une_prem_reser FLOAT,
            reins_income FLOAT,
            n_sec_tb_income FLOAT,
            n_sec_uw_income FLOAT,
            n_asset_mg_income FLOAT,
            oth_b_income FLOAT,
            fv_value_chg_gain FLOAT,
            invest_income FLOAT,
            ass_invest_income FLOAT,
            forex_gain FLOAT,
            total_cogs FLOAT,
            oper_cost FLOAT,
            int_exp FLOAT,
            comm_exp FLOAT,
            biz_tax_surchg FLOAT,
            sell_exp FLOAT,
            admin_exp FLOAT,
            fin_exp FLOAT,
            assets_impair_loss FLOAT,
            prem_refund FLOAT,
            compens_payout FLOAT,
            reser_insur_liab FLOAT,
            div_payt FLOAT,
            reins_exp FLOAT,
            oper_exp FLOAT,
            compens_payout_refu FLOAT,
            insur_reser_refu FLOAT,
            reins_cost_refund FLOAT,
            other_bus_cost FLOAT,
            operate_profit FLOAT,
            non_oper_income FLOAT,
            non_oper_exp FLOAT,
            nca_disploss FLOAT,
            total_profit FLOAT,
            income_tax FLOAT,
            n_income FLOAT,
            n_income_attr_p FLOAT,
            minority_gain FLOAT,
            oth_compr_income FLOAT,
            t_compr_income FLOAT,
            compr_inc_attr_p FLOAT,
            compr_inc_attr_m_s FLOAT,
            ebit FLOAT,
            ebitda FLOAT,
            insurance_exp FLOAT,
            undist_profit FLOAT,
            distable_profit FLOAT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ts_code, end_date, report_type)
        )
        """
        self._execute_sql(income_sql)
        self._create_index('income_statement', ['ts_code', 'end_date'])
        
        # 资产负债表
        balance_sql = """
        CREATE TABLE IF NOT EXISTS balance_sheet (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ts_code VARCHAR(20),
            ann_date DATE,
            f_ann_date DATE,
            end_date DATE,
            report_type VARCHAR(10),
            comp_type VARCHAR(10),
            total_share FLOAT,
            cap_rese FLOAT,
            undistr_porfit FLOAT,
            surplus_rese FLOAT,
            special_rese FLOAT,
            money_cap FLOAT,
            trad_asset FLOAT,
            notes_receiv FLOAT,
            accounts_receiv FLOAT,
            oth_receiv FLOAT,
            prepayment FLOAT,
            div_receiv FLOAT,
            int_receiv FLOAT,
            inventories FLOAT,
            amor_exp FLOAT,
            nca_within_1y FLOAT,
            sett_rsrv FLOAT,
            loanto_oth_bank_fi FLOAT,
            premium_receiv FLOAT,
            reinsur_receiv FLOAT,
            reinsur_res_receiv FLOAT,
            pur_resale_fa FLOAT,
            oth_cur_assets FLOAT,
            total_cur_assets FLOAT,
            fa_avail_for_sale FLOAT,
            htm_invest FLOAT,
            lt_eqt_invest FLOAT,
            invest_real_estate FLOAT,
            time_deposits FLOAT,
            oth_assets FLOAT,
            lt_rec FLOAT,
            fix_assets FLOAT,
            cip FLOAT,
            const_materials FLOAT,
            fixed_assets_disp FLOAT,
            produc_bio_assets FLOAT,
            oil_and_gas_assets FLOAT,
            intan_assets FLOAT,
            r_and_d FLOAT,
            goodwill FLOAT,
            lt_amor_exp FLOAT,
            defer_tax_assets FLOAT,
            decr_in_disbur FLOAT,
            oth_nca FLOAT,
            total_nca FLOAT,
            cash_reser_cb FLOAT,
            depos_in_oth_bfi FLOAT,
            prec_metals FLOAT,
            deriv_assets FLOAT,
            rr_reins_une_prem FLOAT,
            rr_reins_outstd_cla FLOAT,
            rr_reins_lins_liab FLOAT,
            rr_reins_lthins_liab FLOAT,
            refund_depos FLOAT,
            ph_pledge_loans FLOAT,
            refund_cap_depos FLOAT,
            indep_acct_assets FLOAT,
            client_depos FLOAT,
            client_prov FLOAT,
            transac_seat_fee FLOAT,
            invest_as_receiv FLOAT,
            total_assets FLOAT,
            lt_borr FLOAT,
            st_borr FLOAT,
            cb_borr FLOAT,
            depos_ib_deposits FLOAT,
            loan_oth_bank FLOAT,
            trading_fl FLOAT,
            notes_payable FLOAT,
            acct_payable FLOAT,
            adv_receipts FLOAT,
            sold_for_repur_fa FLOAT,
            comm_payable FLOAT,
            payroll_payable FLOAT,
            taxes_payable FLOAT,
            int_payable FLOAT,
            div_payable FLOAT,
            oth_payable FLOAT,
            acc_exp FLOAT,
            deferred_inc FLOAT,
            st_bonds_payable FLOAT,
            payable_to_reinsurer FLOAT,
            rsrv_insur_cont FLOAT,
            acting_trading_sec FLOAT,
            acting_uw_sec FLOAT,
            non_cur_liab_due_1y FLOAT,
            oth_cur_liab FLOAT,
            total_cur_liab FLOAT,
            bond_payable FLOAT,
            lt_payable FLOAT,
            specific_payables FLOAT,
            estimated_liab FLOAT,
            defer_tax_liab FLOAT,
            defer_inc_non_cur_liab FLOAT,
            oth_ncl FLOAT,
            total_ncl FLOAT,
            depos_oth_bfi FLOAT,
            deriv_liab FLOAT,
            depos FLOAT,
            agency_bus_liab FLOAT,
            oth_liab FLOAT,
            prem_receiv_adva FLOAT,
            depos_received FLOAT,
            ph_invest FLOAT,
            reser_une_prem FLOAT,
            reser_outstd_claims FLOAT,
            reser_lins_liab FLOAT,
            reser_lthins_liab FLOAT,
            indept_acc_liab FLOAT,
            pledge_borr FLOAT,
            indem_payable FLOAT,
            policy_div_payable FLOAT,
            total_liab FLOAT,
            treasury_share FLOAT,
            ordin_risk_reser FLOAT,
            forex_differ FLOAT,
            invest_loss_unconf FLOAT,
            minority_int FLOAT,
            total_hldr_eqy_exc_min_int FLOAT,
            total_hldr_eqy_inc_min_int FLOAT,
            total_liab_hldr_eqy FLOAT,
            lt_payroll_payable FLOAT,
            oth_comp_income FLOAT,
            oth_eqt_tools FLOAT,
            oth_eqt_tools_p_shr FLOAT,
            lending_funds FLOAT,
            acc_receivable FLOAT,
            st_fin_payable FLOAT,
            payables FLOAT,
            hfs_assets FLOAT,
            hfs_sales FLOAT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ts_code, end_date, report_type)
        )
        """
        self._execute_sql(balance_sql)
        self._create_index('balance_sheet', ['ts_code', 'end_date'])
        
        # 现金流量表
        cashflow_sql = """
        CREATE TABLE IF NOT EXISTS cashflow_statement (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ts_code VARCHAR(20),
            ann_date DATE,
            f_ann_date DATE,
            end_date DATE,
            report_type VARCHAR(10),
            comp_type VARCHAR(10),
            net_profit FLOAT,
            finan_exp FLOAT,
            c_fr_sale_sg FLOAT,
            recp_tax_rends FLOAT,
            n_depos_incr_fi FLOAT,
            n_incr_loans_cb FLOAT,
            n_inc_borr_oth_fi FLOAT,
            prem_fr_orig_contr FLOAT,
            n_incr_insured_dep FLOAT,
            n_reinsur_prem FLOAT,
            n_incr_disp_tfa FLOAT,
            ifc_cash_incr FLOAT,
            n_incr_disp_faas FLOAT,
            n_incr_loans_oth_bank FLOAT,
            n_cap_incr_repur FLOAT,
            c_fr_oth_operate_a FLOAT,
            c_inf_fr_operate_a FLOAT,
            c_paid_goods_s FLOAT,
            c_paid_to_for_empl FLOAT,
            c_paid_for_taxes FLOAT,
            n_incr_clt_loan_adv FLOAT,
            n_incr_dep_cbob FLOAT,
            c_pay_claims_orig_inco FLOAT,
            pay_handling_chrg FLOAT,
            pay_comm_insur_plcy FLOAT,
            oth_cash_pay_oper_act FLOAT,
            st_cash_out_act FLOAT,
            n_cashflow_act FLOAT,
            oth_recp_ral_inv_act FLOAT,
            c_disp_withdrwl_invest FLOAT,
            c_recp_return_invest FLOAT,
            n_recp_disp_fiolta FLOAT,
            n_recp_disp_sobu FLOAT,
            stot_inflows_inv_act FLOAT,
            c_pay_acq_const_fiolta FLOAT,
            c_paid_invest FLOAT,
            n_disp_subs_oth_biz FLOAT,
            oth_pay_ral_inv_act FLOAT,
            n_incr_pledge_loan FLOAT,
            stot_out_inv_act FLOAT,
            n_cashflow_inv_act FLOAT,
            c_recp_borrow FLOAT,
            proc_issue_bonds FLOAT,
            oth_cash_recp_ral_fnc_act FLOAT,
            stot_cash_in_fnc_act FLOAT,
            free_cashflow FLOAT,
            c_prepay_amt_borr FLOAT,
            c_pay_dist_dpcp_int_exp FLOAT,
            incl_dvd_profit_paid_sc_ms FLOAT,
            oth_cashpay_ral_fnc_act FLOAT,
            stot_cashout_fnc_act FLOAT,
            n_cash_flows_fnc_act FLOAT,
            eff_fx_flu_cash FLOAT,
            n_incr_cash_cash_equ FLOAT,
            c_cash_equ_beg_period FLOAT,
            c_cash_equ_end_period FLOAT,
            c_recp_cap_contrib FLOAT,
            incl_cash_rec_saims FLOAT,
            uncon_invest_loss FLOAT,
            prov_depr_assets FLOAT,
            depr_fa_coga_dpba FLOAT,
            amort_intang_assets FLOAT,
            lt_amort_deferred_exp FLOAT,
            decr_deferred_exp FLOAT,
            incr_acc_exp FLOAT,
            loss_disp_fiolta FLOAT,
            loss_scr_fa FLOAT,
            loss_fv_chg FLOAT,
            invest_loss FLOAT,
            decr_def_inc_tax_assets FLOAT,
            incr_def_inc_tax_liab FLOAT,
            decr_inventories FLOAT,
            decr_oper_payable FLOAT,
            incr_oper_payable FLOAT,
            others FLOAT,
            im_net_cashflow_oper_act FLOAT,
            conv_debt_into_cap FLOAT,
            conv_copbonds_due_within_1y FLOAT,
            fa_fnc_leases FLOAT,
            end_bal_cash FLOAT,
            beg_bal_cash FLOAT,
            end_bal_cash_equ FLOAT,
            beg_bal_cash_equ FLOAT,
            im_n_incr_cash_equ FLOAT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ts_code, end_date, report_type)
        )
        """
        self._execute_sql(cashflow_sql)
        self._create_index('cashflow_statement', ['ts_code', 'end_date'])
    
    def _create_factor_tables(self):
        """创建因子表"""
        # 技术指标因子表
        technical_sql = """
        CREATE TABLE IF NOT EXISTS technical_factors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ts_code VARCHAR(20),
            trade_date DATE,
            ma_5 FLOAT,
            ma_10 FLOAT,
            ma_20 FLOAT,
            ma_60 FLOAT,
            rsi_14 FLOAT,
            macd FLOAT,
            macd_signal FLOAT,
            macd_hist FLOAT,
            boll_upper FLOAT,
            boll_middle FLOAT,
            boll_lower FLOAT,
            kdj_k FLOAT,
            kdj_d FLOAT,
            kdj_j FLOAT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ts_code, trade_date)
        )
        """
        self._execute_sql(technical_sql)
        self._create_index('technical_factors', ['ts_code', 'trade_date'])
        
        # 基本面因子表
        fundamental_sql = """
        CREATE TABLE IF NOT EXISTS fundamental_factors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ts_code VARCHAR(20),
            trade_date DATE,
            pe_ratio FLOAT,
            pb_ratio FLOAT,
            ps_ratio FLOAT,
            roe FLOAT,
            roa FLOAT,
            debt_ratio FLOAT,
            current_ratio FLOAT,
            quick_ratio FLOAT,
            gross_margin FLOAT,
            net_margin FLOAT,
            asset_turnover FLOAT,
            equity_multiplier FLOAT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ts_code, trade_date)
        )
        """
        self._execute_sql(fundamental_sql)
        self._create_index('fundamental_factors', ['ts_code', 'trade_date'])
    
    def _execute_sql(self, sql: str):
        """执行SQL语句"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text(sql))
                conn.commit()
        except Exception as e:
            logger.error(f"SQL执行失败: {sql[:100]}... 错误: {e}")
            raise
    
    def _create_index(self, table_name: str, columns: List[str]):
        """创建索引"""
        index_name = f"idx_{table_name}_{'_'.join(columns)}"
        columns_str = ', '.join(columns)
        sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns_str})"
        try:
            self._execute_sql(sql)
            logger.debug(f"索引创建成功: {index_name}")
        except Exception as e:
            logger.warning(f"索引创建失败: {index_name}, 错误: {e}")
    
    def insert_dataframe(self, df: pd.DataFrame, table_name: str, if_exists: str = 'append'):
        """插入DataFrame到数据库"""
        try:
            df.to_sql(table_name, self.engine, if_exists=if_exists, index=False, method='multi')
            logger.debug(f"数据插入成功: {table_name}, 行数: {len(df)}")
        except Exception as e:
            logger.error(f"数据插入失败: {table_name}, 错误: {e}")
            raise
    
    def query_dataframe(self, sql: str) -> pd.DataFrame:
        """查询数据返回DataFrame"""
        try:
            df = pd.read_sql(sql, self.engine)
            return df
        except Exception as e:
            logger.error(f"查询失败: {sql[:100]}... 错误: {e}")
            raise
    
    def get_existing_data_range(self, table_name: str, ts_code: str = None) -> Dict[str, Any]:
        """获取已有数据的日期范围"""
        try:
            # 根据表名确定日期列名
            date_column = 'cal_date' if table_name == 'trade_calendar' else 'trade_date'

            if ts_code:
                sql = f"""
                SELECT MIN({date_column}) as min_date, MAX({date_column}) as max_date, COUNT(*) as count
                FROM {table_name} WHERE ts_code = '{ts_code}'
                """
            else:
                sql = f"""
                SELECT MIN({date_column}) as min_date, MAX({date_column}) as max_date, COUNT(*) as count
                FROM {table_name}
                """
            
            result = self.query_dataframe(sql)
            if not result.empty and result.iloc[0]['count'] > 0:
                return {
                    'min_date': result.iloc[0]['min_date'],
                    'max_date': result.iloc[0]['max_date'],
                    'count': result.iloc[0]['count']
                }
            return {'min_date': None, 'max_date': None, 'count': 0}
        except Exception as e:
            logger.error(f"获取数据范围失败: {e}")
            return {'min_date': None, 'max_date': None, 'count': 0}
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()
