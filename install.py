"""
安装脚本 - 自动安装依赖和初始化环境
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n正在{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✓ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    print(f"✓ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """安装依赖包"""
    commands = [
        ("pip install --upgrade pip", "升级pip"),
        ("pip install -r requirements.txt", "安装项目依赖"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    return True


def create_directories():
    """创建必要的目录"""
    directories = ["data", "logs", "backup", "tests"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✓ 目录结构创建完成")


def test_tushare_connection():
    """测试Tushare连接"""
    try:
        import tushare as ts
        from src.config import config
        
        # 设置token
        ts.set_token(config.tushare_token)
        pro = ts.pro_api()
        
        # 测试API调用
        df = pro.trade_cal(exchange='', start_date='20240101', end_date='20240102')
        if not df.empty:
            print("✓ Tushare连接测试成功")
            return True
        else:
            print("✗ Tushare连接测试失败: 返回数据为空")
            return False
            
    except Exception as e:
        print(f"✗ Tushare连接测试失败: {e}")
        return False


def main():
    """主安装流程"""
    print("=" * 50)
    print("因子数据库构建系统 - 环境安装")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 安装依赖
    if not install_dependencies():
        print("\n安装失败，请检查错误信息")
        sys.exit(1)
    
    # 测试Tushare连接
    if not test_tushare_connection():
        print("\n警告: Tushare连接测试失败，请检查token配置")
    
    print("\n" + "=" * 50)
    print("✓ 环境安装完成！")
    print("=" * 50)
    print("\n下一步:")
    print("1. 检查 config.yaml 中的配置")
    print("2. 运行 python main.py 开始构建因子数据库")


if __name__ == "__main__":
    main()
