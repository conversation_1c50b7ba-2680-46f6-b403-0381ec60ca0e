"""
行情数据获取模块
"""
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from loguru import logger
from src.api_manager import api_manager
from src.database import db_manager
from src.progress_manager import progress_manager
from src.data_fetcher import data_fetcher
from src.config import config


class MarketDataFetcher:
    """行情数据获取器"""
    
    def __init__(self):
        self.start_date = config.get('data_config.start_date', '20100101')
        self.batch_size = config.get('data_config.batch_size', 1000)
        self.sleep_between_batches = config.get('data_config.sleep_between_batches', 0.5)
    
    def fetch_daily_price_data(self, start_date: str = None, end_date: str = None,
                              stock_codes: List[str] = None, force_update: bool = False) -> bool:
        """
        获取日线价格数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            stock_codes: 股票代码列表
            force_update: 是否强制更新
        
        Returns:
            是否成功
        """
        task_name = "daily_price"
        
        try:
            # 设置默认参数
            if start_date is None:
                start_date = self.start_date
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            if stock_codes is None:
                stock_codes = data_fetcher.get_stock_list()
            
            logger.info(f"开始获取日线价格数据: {len(stock_codes)} 只股票, {start_date} - {end_date}")
            
            # 创建日线价格表（如果不存在）
            self._create_daily_price_table()
            
            success_count = 0
            failed_count = 0
            
            # 按股票获取数据
            for i, stock_code in enumerate(stock_codes):
                # 检查是否已处理
                stock_task_name = f"{task_name}_{stock_code}"
                if not force_update and progress_manager.is_stock_completed(stock_code, task_name):
                    success_count += 1
                    logger.debug(f"股票 {stock_code} 已处理，跳过")
                    continue
                
                logger.info(f"获取股票 {stock_code} 数据 ({i+1}/{len(stock_codes)})")
                
                # 获取该股票的价格数据
                df = api_manager.get_daily(
                    ts_code=stock_code,
                    start_date=start_date,
                    end_date=end_date,
                    fields='ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount'
                )
                
                if df is not None and not df.empty:
                    # 数据清洗
                    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
                    df['created_at'] = datetime.now()
                    
                    # 保存到数据库
                    db_manager.insert_dataframe(df, 'daily_price', if_exists='append')
                    
                    # 标记完成
                    progress_manager.mark_stock_completed(stock_code, task_name)
                    success_count += 1
                    
                    logger.debug(f"股票 {stock_code} 完成，获取 {len(df)} 条记录")
                else:
                    logger.warning(f"股票 {stock_code} 没有数据")
                    progress_manager.mark_stock_failed(stock_code, task_name, "无数据返回")
                    failed_count += 1
                
                # 显示进度
                if (i + 1) % 100 == 0:
                    progress_pct = ((i + 1) / len(stock_codes)) * 100
                    logger.info(f"进度: {i+1}/{len(stock_codes)} ({progress_pct:.1f}%), 成功: {success_count}, 失败: {failed_count}")
            
            # 记录总体进度
            progress_manager.set_task_progress(task_name, {
                'status': 'completed',
                'start_date': start_date,
                'end_date': end_date,
                'total_stocks': len(stock_codes),
                'success_count': success_count,
                'failed_count': failed_count,
                'completed_at': datetime.now().isoformat()
            })
            
            logger.info(f"日线价格数据获取完成，成功: {success_count}, 失败: {failed_count}")
            return True
            
        except Exception as e:
            logger.error(f"获取日线价格数据失败: {e}")
            progress_manager.mark_stock_failed('ALL', task_name, str(e))
            return False
    
    def _create_daily_price_table(self):
        """创建日线价格表"""
        sql = """
        CREATE TABLE IF NOT EXISTS daily_price (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ts_code VARCHAR(20),
            trade_date DATE,
            open FLOAT,
            high FLOAT,
            low FLOAT,
            close FLOAT,
            pre_close FLOAT,
            change FLOAT,
            pct_chg FLOAT,
            vol FLOAT,
            amount FLOAT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ts_code, trade_date)
        )
        """
        db_manager._execute_sql(sql)
        db_manager._create_index('daily_price', ['ts_code', 'trade_date'])
        db_manager._create_index('daily_price', ['trade_date'])
    
    def fetch_incremental_daily_data(self, days_back: int = 30) -> bool:
        """
        增量获取最近的日线数据
        
        Args:
            days_back: 向前获取多少天的数据
        
        Returns:
            是否成功
        """
        try:
            # 计算日期范围
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y%m%d')
            
            logger.info(f"增量获取日线数据: {start_date} - {end_date}")
            
            # 获取股票列表
            stock_codes = data_fetcher.get_stock_list()
            if not stock_codes:
                logger.error("没有股票列表")
                return False
            
            return self.fetch_daily_price_data(
                start_date=start_date,
                end_date=end_date,
                stock_codes=stock_codes,
                force_update=True  # 增量更新时强制更新
            )
            
        except Exception as e:
            logger.error(f"增量获取日线数据失败: {e}")
            return False
    
    def get_price_data_summary(self) -> Dict[str, Any]:
        """获取价格数据摘要"""
        try:
            # 检查daily_price表是否存在
            try:
                sql = "SELECT COUNT(*) as total_records FROM daily_price"
                total_records = db_manager.query_dataframe(sql).iloc[0]['total_records']
            except:
                total_records = 0
            
            if total_records == 0:
                return {
                    'total_records': 0,
                    'stock_count': 0,
                    'date_range': None,
                    'latest_date': None
                }
            
            # 获取统计信息
            sql = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT ts_code) as stock_count,
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date
            FROM daily_price
            """
            summary = db_manager.query_dataframe(sql).iloc[0]
            
            return {
                'total_records': summary['total_records'],
                'stock_count': summary['stock_count'],
                'date_range': f"{summary['min_date']} - {summary['max_date']}",
                'latest_date': summary['max_date']
            }
            
        except Exception as e:
            logger.error(f"获取价格数据摘要失败: {e}")
            return {}
    
    def check_missing_data(self, start_date: str = None, end_date: str = None) -> Dict[str, List[str]]:
        """
        检查缺失的数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            缺失数据的股票和日期
        """
        try:
            if start_date is None:
                start_date = self.start_date
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            
            # 获取所有股票和交易日期
            stock_codes = data_fetcher.get_stock_list()
            trading_dates = data_fetcher.get_trading_dates(start_date, end_date)
            
            if not stock_codes or not trading_dates:
                return {'missing_stocks': [], 'missing_dates': []}
            
            # 检查缺失的数据
            missing_combinations = []
            
            for stock_code in stock_codes[:10]:  # 先检查前10只股票作为示例
                sql = f"""
                SELECT COUNT(*) as count FROM daily_price 
                WHERE ts_code = '{stock_code}' 
                AND trade_date >= '{start_date}' 
                AND trade_date <= '{end_date}'
                """
                count = db_manager.query_dataframe(sql).iloc[0]['count']
                expected_count = len(trading_dates)
                
                if count < expected_count:
                    missing_combinations.append(f"{stock_code}: {count}/{expected_count}")
            
            return {
                'missing_combinations': missing_combinations,
                'total_stocks_checked': min(10, len(stock_codes)),
                'expected_records_per_stock': len(trading_dates)
            }
            
        except Exception as e:
            logger.error(f"检查缺失数据失败: {e}")
            return {}


# 全局行情数据获取器实例
market_data_fetcher = MarketDataFetcher()
