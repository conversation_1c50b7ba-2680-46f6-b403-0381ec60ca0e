# Tushare配置
tushare:
  token: "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
  timeout: 30
  retry_count: 3
  retry_delay: 1  # 秒
  max_requests_per_minute: 200  # 根据您的权限调整

# 数据库配置
database:
  type: "sqlite"  # sqlite, mysql, postgresql
  sqlite:
    path: "data/factor_database.db"
  mysql:
    host: "localhost"
    port: 3306
    username: "root"
    password: ""
    database: "factor_db"
    charset: "utf8mb4"

# 数据获取配置
data_config:
  start_date: "20100101"  # 数据开始日期
  batch_size: 1000  # 每批处理的股票数量
  sleep_between_batches: 0.5  # 批次间休息时间（秒）
  
# 因子计算配置
factors:
  technical:
    - "MA_5"
    - "MA_10"
    - "MA_20"
    - "MA_60"
    - "RSI_14"
    - "MACD"
    - "BOLL"
    - "KDJ"
  fundamental:
    - "PE"
    - "PB"
    - "ROE"
    - "ROA"
    - "DEBT_RATIO"
    - "CURRENT_RATIO"

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "100 MB"
  retention: "30 days"
  log_file: "logs/factor_db.log"

# 进度管理
progress:
  checkpoint_file: "data/progress.json"
  backup_interval: 100  # 每处理多少条记录备份一次进度
