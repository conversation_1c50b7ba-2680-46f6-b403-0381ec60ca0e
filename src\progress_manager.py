"""
进度管理模块 - 解决断点续传问题
"""
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
from loguru import logger
from src.config import config


class ProgressManager:
    """进度管理器，支持断点续传"""
    
    def __init__(self, checkpoint_file: Optional[str] = None):
        self.checkpoint_file = checkpoint_file or config.get('progress.checkpoint_file', 'data/progress.json')
        self.progress_data = self._load_progress()
        self.backup_interval = config.get('progress.backup_interval', 100)
        self._operation_count = 0
    
    def _load_progress(self) -> Dict[str, Any]:
        """加载进度数据"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"进度数据加载成功: {self.checkpoint_file}")
                return data
            except (json.JSONDecodeError, FileNotFoundError) as e:
                logger.warning(f"进度文件加载失败: {e}，将创建新的进度文件")
        
        return {
            'created_at': datetime.now().isoformat(),
            'last_updated': datetime.now().isoformat(),
            'tasks': {},
            'completed_stocks': [],
            'failed_stocks': [],
            'data_ranges': {}
        }
    
    def save_progress(self):
        """保存进度数据"""
        self.progress_data['last_updated'] = datetime.now().isoformat()
        
        # 确保目录存在
        Path(self.checkpoint_file).parent.mkdir(exist_ok=True)
        
        try:
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
            logger.debug("进度数据已保存")
        except Exception as e:
            logger.error(f"保存进度数据失败: {e}")
    
    def auto_save(self):
        """自动保存（根据操作次数）"""
        self._operation_count += 1
        if self._operation_count >= self.backup_interval:
            self.save_progress()
            self._operation_count = 0
    
    def set_task_progress(self, task_name: str, progress_info: Dict[str, Any]):
        """设置任务进度"""
        self.progress_data['tasks'][task_name] = {
            **progress_info,
            'updated_at': datetime.now().isoformat()
        }
        self.auto_save()
    
    def get_task_progress(self, task_name: str) -> Optional[Dict[str, Any]]:
        """获取任务进度"""
        return self.progress_data['tasks'].get(task_name)
    
    def is_task_completed(self, task_name: str) -> bool:
        """检查任务是否完成"""
        task_progress = self.get_task_progress(task_name)
        return task_progress and task_progress.get('status') == 'completed'
    
    def mark_stock_completed(self, stock_code: str, task_name: str):
        """标记股票数据获取完成"""
        key = f"{task_name}_{stock_code}"
        if key not in self.progress_data['completed_stocks']:
            self.progress_data['completed_stocks'].append(key)
        self.auto_save()
    
    def is_stock_completed(self, stock_code: str, task_name: str) -> bool:
        """检查股票数据是否已获取"""
        key = f"{task_name}_{stock_code}"
        return key in self.progress_data['completed_stocks']
    
    def mark_stock_failed(self, stock_code: str, task_name: str, error: str):
        """标记股票数据获取失败"""
        failure_info = {
            'stock_code': stock_code,
            'task_name': task_name,
            'error': error,
            'failed_at': datetime.now().isoformat()
        }
        self.progress_data['failed_stocks'].append(failure_info)
        self.auto_save()
    
    def get_failed_stocks(self, task_name: str) -> List[str]:
        """获取失败的股票列表"""
        return [
            item['stock_code'] for item in self.progress_data['failed_stocks']
            if item['task_name'] == task_name
        ]
    
    def set_data_range(self, data_type: str, start_date: str, end_date: str):
        """设置数据范围"""
        self.progress_data['data_ranges'][data_type] = {
            'start_date': start_date,
            'end_date': end_date,
            'updated_at': datetime.now().isoformat()
        }
        self.auto_save()
    
    def get_data_range(self, data_type: str) -> Optional[Dict[str, str]]:
        """获取数据范围"""
        return self.progress_data['data_ranges'].get(data_type)
    
    def reset_task(self, task_name: str):
        """重置任务进度"""
        if task_name in self.progress_data['tasks']:
            del self.progress_data['tasks'][task_name]
        
        # 清除相关的完成记录
        self.progress_data['completed_stocks'] = [
            item for item in self.progress_data['completed_stocks']
            if not item.startswith(f"{task_name}_")
        ]
        
        # 清除相关的失败记录
        self.progress_data['failed_stocks'] = [
            item for item in self.progress_data['failed_stocks']
            if item['task_name'] != task_name
        ]
        
        self.save_progress()
        logger.info(f"任务 {task_name} 进度已重置")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取进度摘要"""
        return {
            'total_tasks': len(self.progress_data['tasks']),
            'completed_tasks': len([
                task for task in self.progress_data['tasks'].values()
                if task.get('status') == 'completed'
            ]),
            'total_completed_stocks': len(self.progress_data['completed_stocks']),
            'total_failed_stocks': len(self.progress_data['failed_stocks']),
            'data_ranges': self.progress_data['data_ranges'],
            'last_updated': self.progress_data['last_updated']
        }


# 全局进度管理器实例
progress_manager = ProgressManager()
