"""
简单的tushare连接测试
"""
import tushare as ts
import pandas as pd

# 设置token
token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
ts.set_token(token)
pro = ts.pro_api()

print("开始测试tushare连接...")

try:
    # 测试获取交易日历
    print("测试1: 获取交易日历")
    df = pro.trade_cal(exchange='', start_date='20240101', end_date='20240105')
    print(f"交易日历数据: {len(df)} 行")
    print(df.head())
    print()
    
    # 测试获取股票基本信息
    print("测试2: 获取股票基本信息")
    df = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry')
    print(f"股票基本信息: {len(df)} 行")
    print(df.head())
    print()
    
    # 测试获取每日基本面数据
    print("测试3: 获取每日基本面数据")
    df = pro.daily_basic(trade_date='20240103', fields='ts_code,trade_date,close,pe,pb')
    print(f"每日基本面数据: {len(df)} 行")
    print(df.head())
    print()
    
    print("✓ 所有测试通过！tushare连接正常")
    
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()
