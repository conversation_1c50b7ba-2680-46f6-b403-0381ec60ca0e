"""
日志管理模块
"""
import sys
from loguru import logger
from pathlib import Path
from src.config import config


def setup_logger():
    """设置日志系统"""
    # 移除默认的日志处理器
    logger.remove()
    
    # 获取日志配置
    log_config = config.logging_config
    log_level = log_config.get('level', 'INFO')
    log_format = log_config.get('format', 
                               "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
    log_file = log_config.get('log_file', 'logs/factor_db.log')
    rotation = log_config.get('rotation', '100 MB')
    retention = log_config.get('retention', '30 days')
    
    # 确保日志目录存在
    Path(log_file).parent.mkdir(exist_ok=True)
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format=log_format,
        level=log_level,
        colorize=True
    )
    
    # 添加文件输出
    logger.add(
        log_file,
        format=log_format,
        level=log_level,
        rotation=rotation,
        retention=retention,
        encoding='utf-8'
    )
    
    logger.info("日志系统初始化完成")
    return logger


# 初始化日志
setup_logger()
