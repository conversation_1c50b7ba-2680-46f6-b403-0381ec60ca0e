"""
数据获取模块 - 获取基础数据
"""
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from loguru import logger
from src.api_manager import api_manager
from src.database import db_manager
from src.progress_manager import progress_manager
from src.config import config


class DataFetcher:
    """数据获取器"""
    
    def __init__(self):
        self.start_date = config.get('data_config.start_date', '20100101')
        self.batch_size = config.get('data_config.batch_size', 1000)
        self.sleep_between_batches = config.get('data_config.sleep_between_batches', 0.5)
    
    def fetch_stock_basic(self, force_update: bool = False) -> bool:
        """
        获取股票基本信息
        
        Args:
            force_update: 是否强制更新
        
        Returns:
            是否成功
        """
        task_name = "stock_basic"
        
        # 检查是否已完成
        if not force_update and progress_manager.is_task_completed(task_name):
            logger.info("股票基本信息已获取，跳过")
            return True
        
        try:
            logger.info("开始获取股票基本信息...")
            
            # 获取数据
            df = api_manager.get_stock_basic(
                exchange='',
                list_status='L',  # 只获取上市的股票
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            if df is None or df.empty:
                logger.error("获取股票基本信息失败")
                return False
            
            # 数据清洗
            df['list_date'] = pd.to_datetime(df['list_date'], format='%Y%m%d', errors='coerce')
            df['created_at'] = datetime.now()
            df['updated_at'] = datetime.now()
            
            # 保存到数据库
            db_manager.insert_dataframe(df, 'stock_basic', if_exists='replace')
            
            # 记录进度
            progress_manager.set_task_progress(task_name, {
                'status': 'completed',
                'total_stocks': len(df),
                'completed_at': datetime.now().isoformat()
            })
            
            logger.info(f"股票基本信息获取完成，共 {len(df)} 只股票")
            return True
            
        except Exception as e:
            logger.error(f"获取股票基本信息失败: {e}")
            progress_manager.mark_stock_failed('ALL', task_name, str(e))
            return False
    
    def fetch_trade_calendar(self, start_date: str = None, end_date: str = None, 
                           force_update: bool = False) -> bool:
        """
        获取交易日历
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            force_update: 是否强制更新
        
        Returns:
            是否成功
        """
        task_name = "trade_calendar"
        
        # 检查是否已完成
        if not force_update and progress_manager.is_task_completed(task_name):
            logger.info("交易日历已获取，跳过")
            return True
        
        try:
            # 设置默认日期范围
            if start_date is None:
                start_date = self.start_date
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            
            logger.info(f"开始获取交易日历: {start_date} - {end_date}")
            
            # 获取数据
            df = api_manager.get_trade_cal(
                exchange='',
                start_date=start_date,
                end_date=end_date
            )
            
            if df is None or df.empty:
                logger.error("获取交易日历失败")
                return False
            
            # 数据清洗
            df['cal_date'] = pd.to_datetime(df['cal_date'], format='%Y%m%d')
            df['created_at'] = datetime.now()
            
            # 保存到数据库
            db_manager.insert_dataframe(df, 'trade_calendar', if_exists='replace')
            
            # 记录进度
            progress_manager.set_task_progress(task_name, {
                'status': 'completed',
                'start_date': start_date,
                'end_date': end_date,
                'total_days': len(df),
                'trading_days': len(df[df['is_open'] == 1]),
                'completed_at': datetime.now().isoformat()
            })
            
            logger.info(f"交易日历获取完成，共 {len(df)} 天，其中交易日 {len(df[df['is_open'] == 1])} 天")
            return True
            
        except Exception as e:
            logger.error(f"获取交易日历失败: {e}")
            progress_manager.mark_stock_failed('ALL', task_name, str(e))
            return False
    
    def get_stock_list(self) -> List[str]:
        """获取股票列表"""
        try:
            sql = "SELECT ts_code FROM stock_basic ORDER BY ts_code"
            df = db_manager.query_dataframe(sql)
            return df['ts_code'].tolist()
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def get_trading_dates(self, start_date: str = None, end_date: str = None) -> List[str]:
        """获取交易日期列表"""
        try:
            if start_date is None:
                start_date = self.start_date
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            
            sql = f"""
            SELECT cal_date FROM trade_calendar 
            WHERE is_open = 1 
            AND cal_date >= '{start_date}' 
            AND cal_date <= '{end_date}'
            ORDER BY cal_date
            """
            df = db_manager.query_dataframe(sql)
            return df['cal_date'].dt.strftime('%Y%m%d').tolist()
        except Exception as e:
            logger.error(f"获取交易日期失败: {e}")
            return []
    
    def fetch_daily_basic_data(self, start_date: str = None, end_date: str = None,
                              stock_codes: List[str] = None, force_update: bool = False) -> bool:
        """
        获取每日基本面数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            stock_codes: 股票代码列表，None表示所有股票
            force_update: 是否强制更新
        
        Returns:
            是否成功
        """
        task_name = "daily_basic"
        
        try:
            # 设置默认参数
            if start_date is None:
                start_date = self.start_date
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            if stock_codes is None:
                stock_codes = self.get_stock_list()
            
            # 获取交易日期
            trading_dates = self.get_trading_dates(start_date, end_date)
            if not trading_dates:
                logger.error("没有找到交易日期")
                return False
            
            logger.info(f"开始获取每日基本面数据: {len(stock_codes)} 只股票, {len(trading_dates)} 个交易日")
            
            total_combinations = len(stock_codes) * len(trading_dates)
            processed = 0
            
            # 按日期获取数据
            for trade_date in trading_dates:
                # 检查该日期是否已处理
                date_task_name = f"{task_name}_{trade_date}"
                if not force_update and progress_manager.is_task_completed(date_task_name):
                    processed += len(stock_codes)
                    logger.debug(f"日期 {trade_date} 已处理，跳过")
                    continue
                
                logger.info(f"获取日期 {trade_date} 的数据...")
                
                # 获取该日期的所有股票数据
                df = api_manager.get_daily_basic(
                    trade_date=trade_date,
                    fields='ts_code,trade_date,close,turnover_rate,turnover_rate_f,volume_ratio,'
                           'pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,total_share,float_share,'
                           'free_share,total_mv,circ_mv'
                )
                
                if df is not None and not df.empty:
                    # 数据清洗
                    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
                    df['created_at'] = datetime.now()
                    
                    # 保存到数据库
                    db_manager.insert_dataframe(df, 'daily_basic', if_exists='append')
                    
                    # 记录进度
                    progress_manager.set_task_progress(date_task_name, {
                        'status': 'completed',
                        'trade_date': trade_date,
                        'stock_count': len(df),
                        'completed_at': datetime.now().isoformat()
                    })
                    
                    processed += len(df)
                    logger.info(f"日期 {trade_date} 完成，获取 {len(df)} 条记录")
                else:
                    logger.warning(f"日期 {trade_date} 没有数据")
                    progress_manager.mark_stock_failed(trade_date, task_name, "无数据返回")
                
                # 显示进度
                progress_pct = (processed / total_combinations) * 100
                logger.info(f"总进度: {processed}/{total_combinations} ({progress_pct:.1f}%)")
            
            # 记录总体进度
            progress_manager.set_task_progress(task_name, {
                'status': 'completed',
                'start_date': start_date,
                'end_date': end_date,
                'total_stocks': len(stock_codes),
                'total_dates': len(trading_dates),
                'processed_records': processed,
                'completed_at': datetime.now().isoformat()
            })
            
            logger.info(f"每日基本面数据获取完成，共处理 {processed} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"获取每日基本面数据失败: {e}")
            progress_manager.mark_stock_failed('ALL', task_name, str(e))
            return False
    
    def check_data_integrity(self) -> Dict[str, Any]:
        """检查数据完整性"""
        try:
            result = {}
            
            # 检查股票基本信息
            stock_count = db_manager.query_dataframe("SELECT COUNT(*) as count FROM stock_basic").iloc[0]['count']
            result['stock_basic'] = {'count': stock_count}
            
            # 检查交易日历
            calendar_range = db_manager.get_existing_data_range('trade_calendar')
            result['trade_calendar'] = calendar_range
            
            # 检查每日基本面数据
            daily_basic_range = db_manager.get_existing_data_range('daily_basic')
            result['daily_basic'] = daily_basic_range
            
            logger.info(f"数据完整性检查完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"数据完整性检查失败: {e}")
            return {}


# 全局数据获取器实例
data_fetcher = DataFetcher()
