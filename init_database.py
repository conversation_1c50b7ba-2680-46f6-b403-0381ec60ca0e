"""
数据库初始化脚本
"""
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.logger import logger
from src.database import db_manager
from src.config import config


def main():
    """初始化数据库"""
    try:
        logger.info("开始初始化数据库...")
        
        # 创建所有表
        db_manager.create_tables()
        
        logger.info("数据库初始化完成！")
        
        # 显示配置信息
        logger.info(f"数据库类型: {config.get('database.type')}")
        if config.get('database.type') == 'sqlite':
            logger.info(f"数据库文件: {config.get('database.sqlite.path')}")
        
        logger.info("可以开始数据获取了！")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
