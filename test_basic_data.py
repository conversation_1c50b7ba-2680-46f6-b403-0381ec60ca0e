"""
测试基础数据获取功能
"""
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.logger import logger
from src.api_manager import api_manager
from src.data_fetcher import data_fetcher
from src.progress_manager import progress_manager


def test_api_connection():
    """测试API连接"""
    logger.info("=" * 50)
    logger.info("测试API连接")
    logger.info("=" * 50)
    
    if api_manager.test_connection():
        logger.info("✓ API连接测试成功")
        return True
    else:
        logger.error("✗ API连接测试失败")
        return False


def test_fetch_stock_basic():
    """测试获取股票基本信息"""
    logger.info("=" * 50)
    logger.info("测试获取股票基本信息")
    logger.info("=" * 50)
    
    if data_fetcher.fetch_stock_basic():
        logger.info("✓ 股票基本信息获取成功")
        
        # 显示统计信息
        stock_list = data_fetcher.get_stock_list()
        logger.info(f"共获取 {len(stock_list)} 只股票")
        if stock_list:
            logger.info(f"示例股票代码: {stock_list[:5]}")
        return True
    else:
        logger.error("✗ 股票基本信息获取失败")
        return False


def test_fetch_trade_calendar():
    """测试获取交易日历"""
    logger.info("=" * 50)
    logger.info("测试获取交易日历")
    logger.info("=" * 50)
    
    # 获取最近一年的交易日历
    if data_fetcher.fetch_trade_calendar(start_date='20240101', end_date='20241231'):
        logger.info("✓ 交易日历获取成功")
        
        # 显示统计信息
        trading_dates = data_fetcher.get_trading_dates('20240101', '20241231')
        logger.info(f"2024年共有 {len(trading_dates)} 个交易日")
        if trading_dates:
            logger.info(f"最近的交易日: {trading_dates[-5:]}")
        return True
    else:
        logger.error("✗ 交易日历获取失败")
        return False


def test_fetch_sample_daily_basic():
    """测试获取样本每日基本面数据"""
    logger.info("=" * 50)
    logger.info("测试获取样本每日基本面数据")
    logger.info("=" * 50)
    
    # 获取少量股票的最近几天数据作为测试
    stock_list = data_fetcher.get_stock_list()
    if not stock_list:
        logger.error("没有股票列表，请先获取股票基本信息")
        return False
    
    # 选择前5只股票
    sample_stocks = stock_list[:5]
    logger.info(f"测试股票: {sample_stocks}")
    
    # 获取最近5个交易日的数据
    trading_dates = data_fetcher.get_trading_dates()
    if not trading_dates:
        logger.error("没有交易日期，请先获取交易日历")
        return False
    
    recent_dates = trading_dates[-5:]
    start_date = recent_dates[0]
    end_date = recent_dates[-1]
    
    logger.info(f"测试日期范围: {start_date} - {end_date}")
    
    if data_fetcher.fetch_daily_basic_data(
        start_date=start_date,
        end_date=end_date,
        stock_codes=sample_stocks
    ):
        logger.info("✓ 样本每日基本面数据获取成功")
        return True
    else:
        logger.error("✗ 样本每日基本面数据获取失败")
        return False


def show_progress_summary():
    """显示进度摘要"""
    logger.info("=" * 50)
    logger.info("进度摘要")
    logger.info("=" * 50)
    
    summary = progress_manager.get_summary()
    logger.info(f"总任务数: {summary['total_tasks']}")
    logger.info(f"已完成任务: {summary['completed_tasks']}")
    logger.info(f"已完成股票数据: {summary['total_completed_stocks']}")
    logger.info(f"失败记录数: {summary['total_failed_stocks']}")
    logger.info(f"最后更新时间: {summary['last_updated']}")
    
    # 显示数据范围
    if summary['data_ranges']:
        logger.info("数据范围:")
        for data_type, range_info in summary['data_ranges'].items():
            logger.info(f"  {data_type}: {range_info['start_date']} - {range_info['end_date']}")


def check_data_integrity():
    """检查数据完整性"""
    logger.info("=" * 50)
    logger.info("数据完整性检查")
    logger.info("=" * 50)
    
    integrity = data_fetcher.check_data_integrity()
    
    for table, info in integrity.items():
        logger.info(f"{table}: {info}")


def main():
    """主测试流程"""
    logger.info("开始基础数据获取测试...")
    
    success_count = 0
    total_tests = 4
    
    # 测试API连接
    if test_api_connection():
        success_count += 1
    
    # 测试获取股票基本信息
    if test_fetch_stock_basic():
        success_count += 1
    
    # 测试获取交易日历
    if test_fetch_trade_calendar():
        success_count += 1
    
    # 测试获取样本每日基本面数据
    if test_fetch_sample_daily_basic():
        success_count += 1
    
    # 显示进度摘要
    show_progress_summary()
    
    # 检查数据完整性
    check_data_integrity()
    
    # 总结
    logger.info("=" * 50)
    logger.info("测试总结")
    logger.info("=" * 50)
    logger.info(f"测试通过: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("✓ 所有测试通过！基础数据获取功能正常")
        logger.info("现在可以开始获取完整的历史数据了")
    else:
        logger.warning(f"有 {total_tests - success_count} 个测试失败，请检查配置和网络连接")


if __name__ == "__main__":
    main()
