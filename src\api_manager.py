"""
API管理模块 - 处理tushare API限制和重试机制
"""
import time
import random
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Callable
import pandas as pd
import tushare as ts
from loguru import logger
from src.config import config
from src.progress_manager import progress_manager


class APIManager:
    """API管理器，处理频率限制和重试"""
    
    def __init__(self):
        self.token = config.tushare_token
        self.timeout = config.get('tushare.timeout', 30)
        self.retry_count = config.get('tushare.retry_count', 3)
        self.retry_delay = config.get('tushare.retry_delay', 1)
        self.max_requests_per_minute = config.get('tushare.max_requests_per_minute', 200)
        
        # 请求历史记录
        self.request_history = []
        
        # 初始化tushare
        self._init_tushare()
    
    def _init_tushare(self):
        """初始化tushare"""
        try:
            ts.set_token(self.token)
            self.pro = ts.pro_api()
            logger.info("Tushare API初始化成功")
        except Exception as e:
            logger.error(f"Tushare API初始化失败: {e}")
            raise
    
    def _check_rate_limit(self):
        """检查频率限制"""
        now = datetime.now()
        # 清理1分钟前的请求记录
        self.request_history = [
            req_time for req_time in self.request_history
            if now - req_time < timedelta(minutes=1)
        ]
        
        # 检查是否超过频率限制
        if len(self.request_history) >= self.max_requests_per_minute:
            sleep_time = 60 - (now - self.request_history[0]).total_seconds()
            if sleep_time > 0:
                logger.warning(f"达到频率限制，等待 {sleep_time:.1f} 秒")
                time.sleep(sleep_time)
                # 重新清理请求历史
                now = datetime.now()
                self.request_history = [
                    req_time for req_time in self.request_history
                    if now - req_time < timedelta(minutes=1)
                ]
    
    def _add_request_record(self):
        """添加请求记录"""
        self.request_history.append(datetime.now())
    
    def _handle_api_error(self, error: Exception, attempt: int) -> bool:
        """处理API错误，返回是否应该重试"""
        error_msg = str(error).lower()
        
        # 频率限制错误
        if any(keyword in error_msg for keyword in ['频率', 'rate', 'limit', '限制']):
            sleep_time = min(60 * (attempt + 1), 300)  # 最多等待5分钟
            logger.warning(f"API频率限制，等待 {sleep_time} 秒后重试")
            time.sleep(sleep_time)
            return True
        
        # 网络错误
        elif any(keyword in error_msg for keyword in ['timeout', 'connection', 'network']):
            sleep_time = min(5 * attempt, 30)  # 最多等待30秒
            logger.warning(f"网络错误，等待 {sleep_time} 秒后重试")
            time.sleep(sleep_time)
            return True
        
        # 服务器错误
        elif any(keyword in error_msg for keyword in ['500', '502', '503', '504', 'server']):
            sleep_time = min(10 * attempt, 60)  # 最多等待60秒
            logger.warning(f"服务器错误，等待 {sleep_time} 秒后重试")
            time.sleep(sleep_time)
            return True
        
        # 其他错误不重试
        else:
            logger.error(f"API调用失败，不重试: {error}")
            return False
    
    def call_api(self, api_func: Callable, *args, **kwargs) -> Optional[pd.DataFrame]:
        """
        安全调用API，带重试机制
        
        Args:
            api_func: API函数
            *args: 位置参数
            **kwargs: 关键字参数
        
        Returns:
            DataFrame或None
        """
        for attempt in range(self.retry_count + 1):
            try:
                # 检查频率限制
                self._check_rate_limit()
                
                # 添加随机延迟，避免请求过于密集
                if attempt > 0:
                    random_delay = random.uniform(0.1, 0.5)
                    time.sleep(random_delay)
                
                # 调用API
                func_name = getattr(api_func, '__name__', str(api_func))
                logger.debug(f"调用API: {func_name}, 尝试 {attempt + 1}/{self.retry_count + 1}")
                result = api_func(*args, **kwargs)
                
                # 记录请求
                self._add_request_record()
                
                # 检查结果
                if result is None or (isinstance(result, pd.DataFrame) and result.empty):
                    logger.warning(f"API返回空数据: {api_func.__name__}")
                    return None
                
                logger.debug(f"API调用成功: {func_name}, 返回 {len(result)} 行数据")
                return result
                
            except Exception as e:
                if attempt < self.retry_count:
                    if self._handle_api_error(e, attempt + 1):
                        continue
                    else:
                        break
                else:
                    logger.error(f"API调用最终失败: {func_name}, 错误: {e}")
                    return None
        
        return None
    
    def get_stock_basic(self, **kwargs) -> Optional[pd.DataFrame]:
        """获取股票基本信息"""
        return self.call_api(self.pro.stock_basic, **kwargs)
    
    def get_trade_cal(self, **kwargs) -> Optional[pd.DataFrame]:
        """获取交易日历"""
        return self.call_api(self.pro.trade_cal, **kwargs)
    
    def get_daily_basic(self, **kwargs) -> Optional[pd.DataFrame]:
        """获取每日基本面数据"""
        return self.call_api(self.pro.daily_basic, **kwargs)
    
    def get_daily(self, **kwargs) -> Optional[pd.DataFrame]:
        """获取日线行情"""
        return self.call_api(self.pro.daily, **kwargs)
    
    def get_income(self, **kwargs) -> Optional[pd.DataFrame]:
        """获取利润表数据"""
        return self.call_api(self.pro.income, **kwargs)
    
    def get_balancesheet(self, **kwargs) -> Optional[pd.DataFrame]:
        """获取资产负债表数据"""
        return self.call_api(self.pro.balancesheet, **kwargs)
    
    def get_cashflow(self, **kwargs) -> Optional[pd.DataFrame]:
        """获取现金流量表数据"""
        return self.call_api(self.pro.cashflow, **kwargs)
    
    def get_fina_indicator(self, **kwargs) -> Optional[pd.DataFrame]:
        """获取财务指标数据"""
        return self.call_api(self.pro.fina_indicator, **kwargs)
    
    def batch_get_data(self, api_func: Callable, param_list: list, 
                      batch_size: int = 100, sleep_between_batches: float = 0.5) -> list:
        """
        批量获取数据
        
        Args:
            api_func: API函数
            param_list: 参数列表
            batch_size: 批次大小
            sleep_between_batches: 批次间休息时间
        
        Returns:
            结果列表
        """
        results = []
        total_batches = (len(param_list) + batch_size - 1) // batch_size
        
        for i in range(0, len(param_list), batch_size):
            batch_num = i // batch_size + 1
            batch_params = param_list[i:i + batch_size]
            
            logger.info(f"处理批次 {batch_num}/{total_batches}, 参数数量: {len(batch_params)}")
            
            batch_results = []
            for params in batch_params:
                if isinstance(params, dict):
                    result = self.call_api(api_func, **params)
                else:
                    result = self.call_api(api_func, params)
                
                if result is not None:
                    batch_results.append(result)
            
            results.extend(batch_results)
            
            # 批次间休息
            if i + batch_size < len(param_list):
                time.sleep(sleep_between_batches)
        
        return results
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            result = self.get_trade_cal(exchange='', start_date='20240101', end_date='20240102')
            if result is not None and not result.empty:
                logger.info("API连接测试成功")
                return True
            else:
                logger.error("API连接测试失败: 返回数据为空")
                return False
        except Exception as e:
            logger.error(f"API连接测试失败: {e}")
            return False


# 全局API管理器实例
api_manager = APIManager()
